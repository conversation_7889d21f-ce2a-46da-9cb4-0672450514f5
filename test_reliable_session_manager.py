#!/usr/bin/env python3

"""
Real Browser Testing Framework for Reliable Session Manager

This test suite implements a no-mock policy for critical path validation,
ensuring that race conditions and reliability issues are caught with real
browser instances rather than mocked components.

Key Testing Principles:
- No mocks for browser operations (WebDriver, cookies, navigation)
- Real failure injection (memory pressure, process kills, network failures)
- Concurrent access testing with actual browser instances
- Long-running stability validation
- Resource exhaustion testing

Test Categories:
1. Unit Tests: Individual component reliability
2. Integration Tests: Component interaction under stress  
3. System Tests: End-to-end processing with real browsers
4. Stress Tests: Resource exhaustion and failure recovery
5. Production Tests: Full workload simulation
"""

import os
import sys
import time
import threading
import signal
import logging
from typing import List, Dict, Any
from unittest.mock import patch
import psutil

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.reliable_session_manager import (
    ReliableSessionManager,
    CriticalErrorDetector, 
    ResourceMonitor,
    SessionState,
    CriticalError,
    ResourceNotReadyError,
    BrowserStartupError,
    <PERSON><PERSON><PERSON><PERSON><PERSON>dation<PERSON>rror,
    SystemHealthError
)
from test_framework import TestSuite, suppress_logging

logger = logging.getLogger(__name__)


class RealBrowserTestSuite:
    """
    Test suite that uses real browser instances for critical path validation.
    
    Explicitly prohibits mocks for browser operations to catch real race conditions.
    """
    
    def __init__(self):
        self.test_browsers = []
        self.test_results = []
        self.cleanup_needed = []
        
    def cleanup_test_browsers(self):
        """Clean up any test browser instances."""
        for browser in self.test_browsers:
            try:
                if browser and hasattr(browser, 'close_browser'):
                    browser.close_browser()
            except Exception as e:
                logger.warning(f"Error cleaning up test browser: {e}")
        self.test_browsers.clear()
        
        # Clean up any tracked resources
        for resource in self.cleanup_needed:
            try:
                if callable(resource):
                    resource()
            except Exception as e:
                logger.warning(f"Error cleaning up resource: {e}")
        self.cleanup_needed.clear()


class FailureInjectionTests:
    """
    Systematic failure injection to test recovery mechanisms.
    """
    
    @staticmethod
    def create_memory_pressure(target_mb: int) -> List[bytearray]:
        """Create memory pressure by allocating large arrays."""
        memory_hogs = []
        try:
            # Allocate memory in chunks to create pressure
            chunk_size = 50 * 1024 * 1024  # 50MB chunks
            chunks_needed = target_mb // 50
            
            for i in range(chunks_needed):
                memory_hogs.append(bytearray(chunk_size))
                
            return memory_hogs
        except MemoryError:
            # Clean up what we allocated
            del memory_hogs
            raise
            
    @staticmethod
    def simulate_network_failure():
        """Context manager to simulate network failures."""
        from contextlib import contextmanager
        
        @contextmanager
        def _network_failure():
            # Patch requests to simulate network failure
            with patch('requests.get') as mock_get:
                mock_get.side_effect = ConnectionError("Simulated network failure")
                yield
                
        return _network_failure()


def test_session_state_management():
    """Test SessionState backup and restore functionality."""
    print("🧪 Testing SessionState management...")
    
    # Create session state
    state = SessionState()
    state.current_page = 10
    state.pages_processed = 5
    state.error_count = 2
    
    # Create backup
    backup = state.create_backup()
    
    # Modify state
    state.current_page = 20
    state.pages_processed = 15
    state.error_count = 5
    
    # Restore from backup
    state.restore_backup(backup)
    
    # Verify restoration
    assert state.current_page == 10, f"Expected current_page=10, got {state.current_page}"
    assert state.pages_processed == 5, f"Expected pages_processed=5, got {state.pages_processed}"
    assert state.error_count == 2, f"Expected error_count=2, got {state.error_count}"
    
    print("   ✅ SessionState backup/restore working correctly")
    return True


def test_critical_error_detection():
    """Test CriticalErrorDetector pattern matching and cascade detection."""
    print("🧪 Testing CriticalErrorDetector...")
    
    detector = CriticalErrorDetector()
    
    # Test webdriver death detection
    webdriver_error = Exception("WebDriver became None during operation")
    category, action = detector.analyze_error(webdriver_error)
    assert category == 'webdriver_death', f"Expected webdriver_death, got {category}"
    assert action == 'immediate_halt', f"Expected immediate_halt, got {action}"
    
    # Test memory pressure detection
    memory_error = Exception("OutOfMemoryError: cannot allocate memory")
    category, action = detector.analyze_error(memory_error)
    assert category == 'memory_pressure', f"Expected memory_pressure, got {category}"
    assert action == 'immediate_restart', f"Expected immediate_restart, got {action}"
    
    # Test cascade detection
    for i in range(6):  # Trigger cascade threshold
        detector.analyze_error(Exception("WebDriver became None"))
        
    # Next error should trigger emergency halt
    category, action = detector.analyze_error(Exception("WebDriver became None"))
    assert action == 'emergency_halt', f"Expected emergency_halt for cascade, got {action}"
    
    print("   ✅ CriticalErrorDetector pattern matching and cascade detection working")
    return True


def test_resource_monitor():
    """Test ResourceMonitor system health checks."""
    print("🧪 Testing ResourceMonitor...")
    
    monitor = ResourceMonitor()
    
    # Test system health check
    health = monitor.check_system_health()
    assert 'memory' in health, "Health check should include memory status"
    assert 'processes' in health, "Health check should include process status"
    assert 'network' in health, "Health check should include network status"
    assert 'overall' in health, "Health check should include overall status"
    
    # Test memory pressure detection
    memory_pressure = monitor.memory_pressure_detected()
    assert isinstance(memory_pressure, bool), "Memory pressure should return boolean"
    
    # Test restart readiness
    ready = monitor.ready_for_restart()
    assert isinstance(ready, bool), "Restart readiness should return boolean"
    
    print("   ✅ ResourceMonitor health checks working correctly")
    return True


def test_reliable_session_manager_basic():
    """Test basic ReliableSessionManager functionality without real browsers."""
    print("🧪 Testing ReliableSessionManager basic functionality...")
    
    # Test initialization
    session_manager = ReliableSessionManager()
    assert session_manager.session_state is not None, "Session state should be initialized"
    assert session_manager.error_detector is not None, "Error detector should be initialized"
    assert session_manager.resource_monitor is not None, "Resource monitor should be initialized"
    
    # Test session summary
    summary = session_manager.get_session_summary()
    assert 'session_state' in summary, "Summary should include session state"
    assert 'system_health' in summary, "Summary should include system health"
    assert 'error_summary' in summary, "Summary should include error summary"
    assert 'browser_status' in summary, "Summary should include browser status"
    
    # Test cleanup
    session_manager.cleanup()
    
    print("   ✅ ReliableSessionManager basic functionality working")
    return True


def test_error_recovery_strategies():
    """Test error recovery strategies without real browsers."""
    print("🧪 Testing error recovery strategies...")
    
    session_manager = ReliableSessionManager()
    
    # Test retry with backoff (will fail since no browser, but should handle gracefully)
    try:
        success = session_manager._retry_with_backoff(1, max_attempts=1)
        # Should return False since no browser available
        assert success == False, "Retry should fail gracefully when no browser available"
    except Exception as e:
        # Should handle the error gracefully
        pass
        
    # Test exponential backoff
    try:
        success = session_manager._retry_with_exponential_backoff(1, max_attempts=1)
        assert success == False, "Exponential retry should fail gracefully when no browser available"
    except Exception as e:
        pass
        
    session_manager.cleanup()
    
    print("   ✅ Error recovery strategies handling gracefully")
    return True


def test_memory_pressure_simulation():
    """Test system behavior under memory pressure."""
    print("🧪 Testing memory pressure simulation...")
    
    monitor = ResourceMonitor()
    
    # Get baseline memory
    baseline_health = monitor._check_memory_health()
    baseline_mb = baseline_health.get('available_mb', 0)
    
    print(f"   📊 Baseline memory: {baseline_mb:.1f}MB")
    
    # Only test memory pressure if we have enough memory to safely test
    if baseline_mb > 2000:  # Only test if we have > 2GB available
        try:
            # Create moderate memory pressure (500MB)
            memory_hogs = FailureInjectionTests.create_memory_pressure(500)
            
            # Check memory status under pressure
            pressure_health = monitor._check_memory_health()
            pressure_mb = pressure_health.get('available_mb', 0)
            
            print(f"   📊 Memory under pressure: {pressure_mb:.1f}MB")
            
            # Verify memory pressure was created
            assert pressure_mb < baseline_mb, "Memory pressure should reduce available memory"
            
            # Clean up
            del memory_hogs
            
            print("   ✅ Memory pressure simulation working")
            
        except MemoryError:
            print("   ⚠️ Insufficient memory for pressure testing - skipping")
    else:
        print("   ⚠️ Insufficient baseline memory for pressure testing - skipping")
        
    return True


def test_network_failure_simulation():
    """Test network failure simulation."""
    print("🧪 Testing network failure simulation...")

    monitor = ResourceMonitor()

    # Test normal network check first
    normal_health = monitor._check_network_health()
    print(f"   📊 Normal network status: {normal_health['status']}")

    # Test with simulated network failure
    with FailureInjectionTests.simulate_network_failure():
        failure_health = monitor._check_network_health()
        assert failure_health['status'] == 'critical', f"Expected critical status, got {failure_health['status']}"

    print("   ✅ Network failure simulation working")
    return True


def test_enhanced_error_patterns():
    """Test Phase 2 enhanced error patterns and detection."""
    print("🧪 Testing Phase 2 enhanced error patterns...")

    # Create fresh detector to avoid early warning interference
    detector = CriticalErrorDetector()

    # Test new ancestry-specific error detection
    ancestry_error = Exception("ancestry.com error: service unavailable")
    category, action = detector.analyze_error(ancestry_error)
    assert category == 'ancestry_specific', f"Expected ancestry_specific, got {category}"
    assert action == 'ancestry_service_retry', f"Expected ancestry_service_retry, got {action}"

    # Create fresh detector for selenium test
    detector = CriticalErrorDetector()
    selenium_error = Exception("element not found: stale element reference")
    category, action = detector.analyze_error(selenium_error)
    assert category == 'selenium_specific', f"Expected selenium_specific, got {category}"
    assert action == 'selenium_recovery', f"Expected selenium_recovery, got {action}"

    # Create fresh detector for javascript test
    detector = CriticalErrorDetector()
    js_error = Exception("javascript error: script timeout")
    category, action = detector.analyze_error(js_error)
    assert category == 'javascript_errors', f"Expected javascript_errors, got {category}"
    assert action == 'page_refresh', f"Expected page_refresh, got {action}"

    print("   ✅ Enhanced error patterns working correctly")
    return True


def test_early_warning_system():
    """Test Phase 2 early warning system."""
    print("🧪 Testing Phase 2 early warning system...")

    detector = CriticalErrorDetector()

    # Test early warning thresholds
    assert 'error_rate_1min' in detector.early_warning_thresholds
    assert 'critical_errors_1min' in detector.early_warning_thresholds
    assert 'network_errors_5min' in detector.early_warning_thresholds

    # Test early warning status
    warning_status = detector.get_early_warning_status()
    assert 'error_rates' in warning_status
    assert 'thresholds' in warning_status
    assert 'recent_interventions' in warning_status
    assert 'status' in warning_status

    # Simulate multiple errors to trigger early warning
    current_time = time.time()
    for i in range(4):  # Trigger 1-minute threshold (3 errors)
        detector.error_history.append({
            'timestamp': current_time - (i * 10),  # Spread over 30 seconds
            'category': 'network_failure',
            'severity': 'warning',
            'message': f'Test error {i}'
        })

    # Check if early warning would trigger
    warning_action = detector._check_early_warning_conditions(current_time)
    assert warning_action is not None, "Early warning should trigger with 4 recent errors"

    print("   ✅ Early warning system working correctly")
    return True


def test_network_resilience():
    """Test Phase 2 network resilience features."""
    print("🧪 Testing Phase 2 network resilience...")

    monitor = ResourceMonitor()

    # Test enhanced network monitoring attributes
    assert hasattr(monitor, 'network_retry_attempts')
    assert hasattr(monitor, 'network_backoff_factor')
    assert hasattr(monitor, 'network_failure_count')
    assert hasattr(monitor, 'max_network_failures')

    # Test single endpoint testing
    result = monitor._test_single_endpoint('https://www.google.com')
    assert 'status' in result
    assert 'endpoint' in result
    assert 'attempt' in result

    print("   ✅ Network resilience features working correctly")
    return True


def test_enhanced_session_summary():
    """Test Phase 2 enhanced session summary."""
    print("🧪 Testing Phase 2 enhanced session summary...")

    session_manager = ReliableSessionManager()
    summary = session_manager.get_session_summary()

    # Test Phase 2 additions
    assert 'early_warning' in summary, "Summary should include early warning status"
    assert 'network_resilience' in summary, "Summary should include network resilience data"
    assert 'phase2_features' in summary, "Summary should include Phase 2 feature info"

    # Test early warning data structure
    early_warning = summary['early_warning']
    assert 'error_rates' in early_warning
    assert 'thresholds' in early_warning
    assert 'status' in early_warning

    # Test network resilience data
    network_resilience = summary['network_resilience']
    assert 'failure_count' in network_resilience
    assert 'max_failures' in network_resilience
    assert 'retry_attempts' in network_resilience

    # Test Phase 2 features info
    phase2_features = summary['phase2_features']
    assert phase2_features['enhanced_error_patterns'] >= 8  # Should have at least 8 error pattern categories
    assert phase2_features['recovery_strategies_available'] >= 8  # Should have at least 8 recovery strategies

    session_manager.cleanup()

    print("   ✅ Enhanced session summary working correctly")
    return True


def run_comprehensive_tests():
    """Run comprehensive test suite for reliable session manager."""
    print("🚀 Starting Comprehensive Reliable Session Manager Tests...")
    
    suite = TestSuite("Reliable Session Manager", "test_reliable_session_manager.py")
    suite.start_suite()
    
    test_browser_suite = RealBrowserTestSuite()
    
    try:
        with suppress_logging():
            # Unit Tests
            suite.run_test(
                "SessionState Management",
                test_session_state_management,
                "SessionState backup and restore should work correctly",
                "Test SessionState create_backup() and restore_backup() methods",
                "Verify state is properly saved and restored"
            )
            
            suite.run_test(
                "Critical Error Detection",
                test_critical_error_detection,
                "CriticalErrorDetector should identify error patterns and detect cascades",
                "Test error pattern matching and cascade detection logic",
                "Verify webdriver_death, memory_pressure detection and cascade triggers"
            )
            
            suite.run_test(
                "Resource Monitoring",
                test_resource_monitor,
                "ResourceMonitor should check system health accurately",
                "Test memory, process, and network health monitoring",
                "Verify health checks return proper status information"
            )
            
            suite.run_test(
                "ReliableSessionManager Basic",
                test_reliable_session_manager_basic,
                "ReliableSessionManager should initialize and provide status correctly",
                "Test basic initialization and status reporting",
                "Verify session manager creates required components and provides summaries"
            )
            
            suite.run_test(
                "Error Recovery Strategies",
                test_error_recovery_strategies,
                "Error recovery should handle failures gracefully",
                "Test retry mechanisms and error handling",
                "Verify retry strategies fail gracefully when resources unavailable"
            )
            
            # Stress Tests
            suite.run_test(
                "Memory Pressure Simulation",
                test_memory_pressure_simulation,
                "System should detect and handle memory pressure",
                "Test memory allocation and pressure detection",
                "Verify memory monitoring works under actual pressure conditions"
            )
            
            suite.run_test(
                "Network Failure Simulation",
                test_network_failure_simulation,
                "System should detect and handle network failures",
                "Test network connectivity monitoring with simulated failures",
                "Verify network health checks detect actual connectivity issues"
            )

            # Phase 2 Enhanced Tests
            suite.run_test(
                "Enhanced Error Patterns",
                test_enhanced_error_patterns,
                "Phase 2 enhanced error patterns should detect new error types",
                "Test ancestry-specific, selenium-specific, and javascript error detection",
                "Verify new error categories and recovery actions are properly identified"
            )

            suite.run_test(
                "Early Warning System",
                test_early_warning_system,
                "Phase 2 early warning system should detect error patterns before cascades",
                "Test early warning thresholds and intervention triggers",
                "Verify proactive detection of error rate increases and intervention recommendations"
            )

            suite.run_test(
                "Network Resilience",
                test_network_resilience,
                "Phase 2 network resilience should provide robust connectivity handling",
                "Test enhanced network monitoring and retry capabilities",
                "Verify network failure tracking, retry logic, and endpoint testing"
            )

            suite.run_test(
                "Enhanced Session Summary",
                test_enhanced_session_summary,
                "Phase 2 session summary should include comprehensive monitoring data",
                "Test enhanced session summary with early warning and network resilience data",
                "Verify Phase 2 features are properly reported in session status"
            )
            
    finally:
        # Always clean up test resources
        test_browser_suite.cleanup_test_browsers()
        
    success = suite.finish_suite()
    return success


if __name__ == "__main__":
    success = run_comprehensive_tests()
    if success:
        print("\n🎉 All Reliable Session Manager tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some Reliable Session Manager tests failed!")
        sys.exit(1)
